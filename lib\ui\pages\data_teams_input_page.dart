import 'package:espot/models/teams_model.dart';
import 'package:espot/shared/cache_manager.dart';
import 'package:espot/shared/snackbar.dart';
import 'package:flutter/material.dart';
import 'package:espot/shared/theme.dart';
import 'package:espot/ui/widgets/buttons.dart';
import 'package:espot/ui/widgets/forms.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:uuid/uuid.dart';

class DataTeamsInputPage extends StatefulWidget {
  final TeamsModel? data;
  const DataTeamsInputPage({
    this.data,
    super.key,
  });

  @override
  State<DataTeamsInputPage> createState() => _DataTeamsInputPageState();
}

class _DataTeamsInputPageState extends State<DataTeamsInputPage>
    with CacheManager {
  final urlImageController = TextEditingController(text: '');
  final descController = TextEditingController(text: '');
  final player1Controller = TextEditingController(text: '');
  final player2Controller = TextEditingController(text: '');
  final player3Controller = TextEditingController(text: '');
  final player4Controller = TextEditingController(text: '');
  final player5Controller = TextEditingController(text: '');

  @override
  void initState() {
    super.initState();

    // Populate fields if editing an existing team
    if (widget.data != null) {
      urlImageController.text = widget.data!.image ?? '';
      descController.text = widget.data!.desc ?? '';
      player1Controller.text = widget.data!.player1 ?? '';
      player2Controller.text = widget.data!.player2 ?? '';
      player3Controller.text = widget.data!.player3 ?? '';
      player4Controller.text = widget.data!.player4 ?? '';
      player5Controller.text = widget.data!.player5 ?? '';
    }
  }

  bool validate() {
    if (descController.text.isEmpty ||
        player1Controller.text.isEmpty ||
        player2Controller.text.isEmpty ||
        player3Controller.text.isEmpty ||
        player4Controller.text.isEmpty ||
        player5Controller.text.isEmpty) {
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            widget.data != null ? 'Update Teams' : 'Input Teams',
          ),
        ),
        body: ListView(
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
          ),
          children: [
            const SizedBox(
              height: 30,
            ),
            Container(
              padding: const EdgeInsets.all(22),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: whiteColor,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomFormField(
                    title: 'Image URL',
                    controller: urlImageController,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  CustomFormField(
                    title: 'Player 1',
                    controller: player1Controller,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  CustomFormField(
                    title: 'Player 2',
                    controller: player2Controller,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  CustomFormField(
                    title: 'Player 3',
                    controller: player3Controller,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  CustomFormField(
                    title: 'Player 4',
                    controller: player4Controller,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  CustomFormField(
                    title: 'Player 5',
                    controller: player5Controller,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  CustomFormField(
                    title: 'Description',
                    maxLine: 5,
                    controller: descController,
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  CustomFilledButton(
                    title: 'Continue',
                    onPressed: () async {
                      if (validate()) {
                        // Show loading indicator
                        EasyLoading.show(status: 'Loading...');

                        try {
                          // Get database reference
                          final DatabaseReference dbRef =
                              FirebaseDatabase.instance.ref();

                          // CREATE new team
                          if (widget.data == null) {
                            // Generate new UUID for the team
                            final String teamId = const Uuid().v4();

                            // Create team data map
                            final Map<String, dynamic> teamData = {
                              'desc': descController.text,
                              'player1': player1Controller.text,
                              'player2': player2Controller.text,
                              'player3': player3Controller.text,
                              'player4': player4Controller.text,
                              'player5': player5Controller.text,
                              'image': urlImageController.text,
                              'verified': 0, // Default to unverified
                            };

                            // Save to Firebase
                            await dbRef
                                .child('teams')
                                .child(teamId)
                                .set(teamData);

                            // Success, navigate back
                            EasyLoading.dismiss();
                            if (mounted) {
                              CustomSnackBar.showToast(
                                  context, 'Team created successfully');
                              Navigator.pushNamed(
                                  context, '/data-teams-success');
                            }
                          }
                          else {
                            final Map<String, dynamic> teamData = {
                              'desc': descController.text,
                              'player1': player1Controller.text,
                              'player2': player2Controller.text,
                              'player3': player3Controller.text,
                              'player4': player4Controller.text,
                              'player5': player5Controller.text,
                              'image': urlImageController.text,
                            };

                            // Update in Firebase
                            await dbRef
                                .child('teams')
                                .child(widget.data!.uid!)
                                .update(teamData);

                            // Success, navigate back
                            EasyLoading.dismiss();
                            if (mounted) {
                              CustomSnackBar.showToast(
                                  context, 'Team updated successfully');
                              Navigator.pushNamed(
                                  context, '/data-teams-success-update');
                            }
                          }
                        } catch (e) {
                          // Error handling
                          EasyLoading.dismiss();
                          if (mounted) {
                            CustomSnackBar.showToast(
                                context, 'Error: ${e.toString()}');
                          }
                        }
                      } else {
                        CustomSnackBar.showToast(
                            context, 'All fields are required');
                      }
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 30,
            )
          ],
        ));
  }
}
