import 'package:espot/models/user_model.dart';
import 'package:espot/shared/cache_manager.dart';
import 'package:espot/shared/snackbar.dart';
import 'package:espot/shared/theme.dart';
import 'package:espot/ui/widgets/buttons.dart';
import 'package:espot/ui/widgets/forms.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage> with CacheManager {
  final emailController = TextEditingController(text: '');
  final passwordController = TextEditingController(text: '');

  Future<UserModel?> signInWithEmailAndPassword(
      String email, String password) async {
    try {
      print('🔐 Attempting login with email: $email');

      final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final String uid = credential.user!.uid;
      print('✅ Firebase Auth successful. UID: $uid');

      DatabaseReference ref =
          FirebaseDatabase.instance.ref().child('users').child(uid);
      print('📍 Checking database path: users/$uid');

      final snapshot = await ref.get();
      print('📊 Database snapshot exists: ${snapshot.exists}');

      if (snapshot.exists) {
        final data = snapshot.value as Map<dynamic, dynamic>;
        print('📋 User data found: $data');
        return UserModel.fromMap(data, uid);
      } else {
        print('❌ User data not found in database');
        throw Exception('User data not found in database');
      }
    } on FirebaseAuthException catch (e) {
      print('🚫 Firebase Auth Error: ${e.code} - ${e.message}');
      if (e.code == 'user-not-found') {
        throw Exception('Email tidak ditemukan.');
      } else if (e.code == 'wrong-password') {
        throw Exception('Password yang dimasukkan salah.');
      } else if (e.code == 'invalid-email') {
        throw Exception('Format email tidak valid.');
      } else if (e.code == 'invalid-credential') {
        throw Exception('Email atau password salah.');
      } else {
        throw Exception(e.message ?? 'Terjadi kesalahan yang tidak diketahui.');
      }
    } catch (e) {
      print('💥 Unexpected error: $e');
      throw Exception(e.toString());
    }
  }

  bool validate() {
    if (emailController.text.isEmpty || passwordController.text.isEmpty) {
      return false;
    }
    return true;
  }

  @override
  void initState() {
    removeAll();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: ListView(
      padding: const EdgeInsets.symmetric(
        horizontal: 24,
      ),
      children: [
        Container(
          width: 180,
          height: 180,
          margin: const EdgeInsets.only(
            top: 100,
            bottom: 80,
          ),
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage(
                'assets/img_logo_light.png',
              ),
            ),
          ),
        ),
        Text(
          'Sign In Espot',
          style: blackTextStyle.copyWith(
            fontSize: 20,
            fontWeight: semiBold,
          ),
        ),
        const SizedBox(
          height: 30,
        ),
        Container(
          padding: const EdgeInsets.all(22),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            color: whiteColor,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // NOTE: EMAIL INPUT
              CustomFormField(
                title: 'Email',
                controller: emailController,
              ),
              const SizedBox(
                height: 16,
              ),
              // NOTE: PASSWORD INPUT
              CustomFormField(
                title: 'Password',
                obscureText: true,
                controller: passwordController,
              ),
              const SizedBox(
                height: 8,
              ),
              Align(
                alignment: Alignment.centerRight,
                child: GestureDetector(
                  onTap: () async {
                    Navigator.pushNamed(context, '/reset-password');
                  },
                  child: Text(
                    'Forgot Password',
                    style: redTextStyle,
                  ),
                ),
              ),
              const SizedBox(
                height: 30,
              ),
              CustomFilledButton(
                title: 'Sign In',
                onPressed: () async {
                  try {
                    EasyLoading.show(status: 'loading...');

                    if (!validate()) {
                      EasyLoading.dismiss();
                      CustomSnackBar.showToast(
                        context,
                        'Please fill all the fields',
                      );
                      return;
                    }

                    final user = await signInWithEmailAndPassword(
                      emailController.text,
                      passwordController.text,
                    );

                    EasyLoading.dismiss();

                    if (user != null) {
                      // Navigate to home page
                      Navigator.pushNamedAndRemoveUntil(
                        context,
                        '/home',
                        (route) => false,
                      );
                    }
                  } catch (e) {
                    EasyLoading.dismiss();
                    CustomSnackBar.showToast(
                      context,
                      e.toString(),
                    );
                  }
                },
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 50,
        ),
        CustomTextButton(
          title: 'Create New Account',
          onPressed: () {
            Navigator.pushNamed(context, '/sign-up');
          },
        ),
        const SizedBox(
          height: 50,
        ),
      ],
    ));
  }
}
