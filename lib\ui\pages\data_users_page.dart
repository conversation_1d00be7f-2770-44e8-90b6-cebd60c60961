import 'package:espot/shared/constant.dart';
import 'package:espot/ui/pages/data_users_input_page.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:espot/models/user_model.dart';
import 'package:espot/shared/theme.dart';
import 'package:espot/ui/widgets/data_users_item.dart';
import 'package:espot/ui/widgets/forms.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class DataUsersPage extends StatefulWidget {
  const DataUsersPage({super.key});

  @override
  State<DataUsersPage> createState() => _DataUsersPageState();
}

class _DataUsersPageState extends State<DataUsersPage> {
  final searchController = TextEditingController(text: '');

  UserModel? selectedUsers;
  List<UserModel> usersList = [];
  List<UserModel> allUsersList = []; // Keep original list for search reset

  @override
  void initState() {
    super.initState();
    _fetchUsers(); // Load users when page initializes
    searchController.addListener(() {
      searchUserByName(searchController.text);
    });
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchUsers() async {
    try {
      EasyLoading.show(status: 'Loading users...');
      DatabaseReference usersRef = FirebaseDatabase.instance.ref().child(USERS);
      DatabaseEvent event = await usersRef.once();

      if (event.snapshot.value != null) {
        Map<dynamic, dynamic> usersData =
            event.snapshot.value as Map<dynamic, dynamic>;
        List<UserModel> tempUsersList = [];
        usersData.forEach((uid, userData) {
          tempUsersList.add(UserModel.fromMap(userData, uid));
        });

        setState(() {
          usersList = tempUsersList;
          allUsersList = tempUsersList; // Store original list
        });
      }
      EasyLoading.dismiss();
    } catch (e) {
      EasyLoading.dismiss();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading users: $e')),
        );
      }
    }
  }

  void searchUserByName(String name) {
    String searchName = name.toLowerCase();

    if (searchName.isEmpty) {
      // Reset to show all users when search is empty
      setState(() {
        usersList = allUsersList;
      });
    } else {
      List<UserModel> foundUsers = allUsersList
          .where((user) => user.name!.toLowerCase().contains(searchName))
          .toList();

      setState(() {
        usersList = foundUsers;
      });
    }
  }

  Future<void> _deleteUser(UserModel user) async {
    try {
      EasyLoading.show(status: 'Deleting user...');
      DatabaseReference userRef =
          FirebaseDatabase.instance.ref().child(USERS).child(user.uid!);
      await userRef.remove();

      // Remove from local lists
      setState(() {
        usersList.removeWhere((u) => u.uid == user.uid);
        allUsersList.removeWhere((u) => u.uid == user.uid);
        selectedUsers = null; // Clear selection
      });

      EasyLoading.dismiss();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('User deleted successfully')),
        );
      }
    } catch (e) {
      EasyLoading.dismiss();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting user: $e')),
        );
      }
    }
  }

  void _showDeleteConfirmation(UserModel user) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete User'),
          content: Text('Are you sure you want to delete ${user.name}?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteUser(user);
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Users',
        ),
        actions: [
          IconButton(
            onPressed: () async {
              final result = await Navigator.pushNamed(context, '/user-input');
              if (result == true) {
                // Refresh the list if a user was added/updated
                _fetchUsers();
              }
            },
            icon: const Icon(Icons.add),
            iconSize: 30,
          )
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _fetchUsers,
        child: ListView(
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
          ),
          children: [
            const SizedBox(
              height: 30,
            ),
            Text(
              'Search',
              style: blackTextStyle.copyWith(
                fontSize: 16,
                fontWeight: semiBold,
              ),
            ),
            const SizedBox(
              height: 14,
            ),
            CustomFormField(
              title: 'Search by name...',
              isShowTitle: false,
              controller: searchController,
              prefixIcon: const Icon(Icons.search),
              onChanged: (value) {
                // Search happens automatically via listener
              },
            ),
            const SizedBox(
              height: 40,
            ),
            Row(
              children: [
                Text(
                  'List Users',
                  style: blackTextStyle.copyWith(
                    fontSize: 16,
                    fontWeight: semiBold,
                  ),
                ),
                const Spacer(),
                if (selectedUsers != null)
                  Container(
                    decoration: BoxDecoration(
                      color: lightBackgroundColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Selected: ${selectedUsers!.name}',
                          style: blackTextStyle.copyWith(
                            fontSize: 14,
                            fontWeight: medium,
                          ),
                        ),
                        const SizedBox(width: 10),
                        GestureDetector(
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: blueColor.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(Icons.edit,
                                color: Colors.blue, size: 20),
                          ),
                          onTap: () async {
                            final result = await Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => DataUsersInputPage(
                                  dataUser: selectedUsers!,
                                ),
                              ),
                            );
                            if (result == true) {
                              // Refresh the list if user was updated
                              _fetchUsers();
                            }
                          },
                        ),
                        const SizedBox(width: 10),
                        GestureDetector(
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: redColor.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(Icons.delete,
                                color: Colors.red, size: 20),
                          ),
                          onTap: () {
                            _showDeleteConfirmation(selectedUsers!);
                          },
                        ),
                        const SizedBox(width: 10),
                        GestureDetector(
                          child: Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: greyColor.withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(Icons.close,
                                color: Colors.grey, size: 20),
                          ),
                          onTap: () {
                            setState(() {
                              selectedUsers = null;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            const SizedBox(
              height: 15,
            ),
            usersList.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Text(
                        searchController.text.isNotEmpty
                            ? 'No users found matching "${searchController.text}"'
                            : 'No users found',
                        style: greyTextStyle.copyWith(fontSize: 16),
                      ),
                    ),
                  )
                : ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: usersList.length,
                    itemBuilder: (context, index) {
                      UserModel dataUsers = usersList[index];
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedUsers = dataUsers;
                          });
                        },
                        child: DataUsersItem(
                          dataUser: dataUsers,
                          isSelected: selectedUsers != null
                              ? selectedUsers!.uid == dataUsers.uid
                              : false,
                        ),
                      );
                    },
                  ),
            const SizedBox(
              height: 10,
            ),
          ],
        ),
      ),
    );
  }
}
