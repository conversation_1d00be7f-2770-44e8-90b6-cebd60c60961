import 'package:flutter/material.dart';
import '../../shared/theme.dart';

class EmptyStateWidget extends StatelessWidget {
  final String searchResult;
  final String filterBy;
  final VoidCallback? onClearSearch;
  final VoidCallback? onClearFilter;
  final VoidCallback? onAddNew;

  const EmptyStateWidget({
    super.key,
    required this.searchResult,
    required this.filterBy,
    this.onClearSearch,
    this.onClearFilter,
    this.onAddNew,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.only(top: 50.0),
        child: Column(
          children: [
            Icon(
              searchResult.isNotEmpty ? Icons.search_off : Icons.groups,
              size: 64,
              color: greyColor,
            ),
            const SizedBox(height: 16),
            Text(
              searchResult.isNotEmpty
                  ? 'No teams found for "$searchResult"'
                  : 'No teams found',
              style: blackTextStyle.copyWith(
                fontSize: 16,
                fontWeight: medium,
              ),
            ),
            if (searchResult.isNotEmpty || filterBy != 'all') ...[
              const SizedBox(height: 8),
              Text(
                'Try adjusting your search or filter criteria',
                style: greyTextStyle.copyWith(fontSize: 14),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (searchResult.isNotEmpty && onClearSearch != null)
                    TextButton(
                      onPressed: onClearSearch,
                      child: Text(
                        'Clear Search',
                        style: blueTextStyle.copyWith(fontSize: 14),
                      ),
                    ),
                  if (searchResult.isNotEmpty &&
                      filterBy != 'all' &&
                      onClearSearch != null &&
                      onClearFilter != null)
                    const SizedBox(width: 16),
                  if (filterBy != 'all' && onClearFilter != null)
                    TextButton(
                      onPressed: onClearFilter,
                      child: Text(
                        'Clear Filter',
                        style: blueTextStyle.copyWith(fontSize: 14),
                      ),
                    ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
