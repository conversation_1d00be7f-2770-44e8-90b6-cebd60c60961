import 'package:flutter/material.dart';
import '../../shared/theme.dart';
import '../../models/teams_model.dart';

class TeamsHeaderWidget extends StatelessWidget {
  final TeamsModel? selectedTeams;
  final String filterBy;
  final VoidCallback onFilterPressed;
  final VoidCallback onSortPressed;

  const TeamsHeaderWidget({
    super.key,
    required this.selectedTeams,
    required this.filterBy,
    required this.onFilterPressed,
    required this.onSortPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Text(
          'List Teams',
          style: blackTextStyle.copyWith(
            fontSize: 16,
            fontWeight: semiBold,
          ),
        ),
        const Spacer(),
        if (selectedTeams != null)
          Text(
            'Selected: ${selectedTeams!.desc}',
            style: greyTextStyle.copyWith(
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          )
        else
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: onFilterPressed,
                icon: Icon(
                  Icons.filter_list,
                  color: filterBy != 'all' ? redColor : greyColor,
                  size: 20,
                ),
                tooltip: 'Filter teams',
              ),
              IconButton(
                onPressed: onSortPressed,
                icon: Icon(
                  Icons.sort,
                  color: greyColor,
                  size: 20,
                ),
                tooltip: 'Sort teams',
              ),
            ],
          ),
      ],
    );
  }
}
