import 'package:espot/models/user_model.dart';
import 'package:espot/shared/cache_manager.dart';
import 'package:espot/shared/constant.dart';
import 'package:espot/shared/snackbar.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:espot/shared/theme.dart';
import 'package:espot/ui/widgets/buttons.dart';
import 'package:espot/ui/widgets/forms.dart';

class DataUsersInputPage extends StatefulWidget {
  final UserModel? dataUser;

  const DataUsersInputPage({super.key, this.dataUser});

  bool get isEdit => dataUser != null;

  @override
  State<DataUsersInputPage> createState() => _DataUsersInputPageState();
}

class _DataUsersInputPageState extends State<DataUsersInputPage>
    with CacheManager {
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final passwordController =
      TextEditingController(); // Added missing controller

  bool isLoading = false; // Added loading state

  @override
  void initState() {
    super.initState();
    if (widget.isEdit) {
      nameController.text = widget.dataUser!.name!;
      emailController.text = widget.dataUser!.email!;
      phoneController.text = widget.dataUser!.phone!;
    }
  }

  @override
  void dispose() {
    // Dispose controllers to prevent memory leaks
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    passwordController.dispose();
    super.dispose();
  }

  Future<void> addUser(
      String uid, String name, String email, String phone) async {
    DatabaseReference ref =
        FirebaseDatabase.instance.ref().child(USERS).child(uid);
    return ref.set({
      'name': name,
      'email': email,
      'phone': phone,
      'profilePicture': '',
      'verified': false,
    }).then((value) {
      print('User added successfully.');
    }).catchError((error) {
      print('Failed to add user: $error');
      throw error; // Re-throw to handle in calling function
    });
  }

  Future<UserModel?> signUpWithEmailAndPassword(
      String name, String email, String password, String phone) async {
    try {
      UserCredential userCredential = await FirebaseAuth.instance
          .createUserWithEmailAndPassword(email: email, password: password);

      User? user = userCredential.user;
      if (user != null) {
        UserModel userModel = UserModel(
          uid: user.uid,
          name: name,
          email: email,
          phone: phone,
          profilePicture: '',
          verified: false,
        );
        await addUser(user.uid, name, email, phone);
        return userModel;
      }
      return null;
    } on FirebaseAuthException catch (e) {
      print('Firebase Auth failed with error: ${e.code}');
      print('Error message: ${e.message}');

      // Show specific error messages to user
      String errorMessage = 'Failed to create account';
      switch (e.code) {
        case 'email-already-in-use':
          errorMessage = 'This email is already registered';
          break;
        case 'weak-password':
          errorMessage = 'Password is too weak';
          break;
        case 'invalid-email':
          errorMessage = 'Invalid email address';
          break;
        default:
          errorMessage = e.message ?? 'Registration failed';
      }

      if (mounted) {
        CustomSnackBar.showToast(context, errorMessage);
      }
      return null;
    } catch (e) {
      print('Unexpected error: $e');
      if (mounted) {
        CustomSnackBar.showToast(context, 'An unexpected error occurred');
      }
      return null;
    }
  }

  // Added validation method
  bool validate() {
    if (nameController.text.trim().isEmpty) {
      CustomSnackBar.showToast(context, 'Name is required');
      return false;
    }
    if (emailController.text.trim().isEmpty) {
      CustomSnackBar.showToast(context, 'Email is required');
      return false;
    }
    if (phoneController.text.trim().isEmpty) {
      CustomSnackBar.showToast(context, 'Phone is required');
      return false;
    }
    // Add email validation
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
        .hasMatch(emailController.text.trim())) {
      CustomSnackBar.showToast(context, 'Please enter a valid email');
      return false;
    }
    return true;
  }

  void getLoadUpdate() {
    if (widget.dataUser != null) {
      nameController.text = widget.dataUser!.name ?? '';
      emailController.text = widget.dataUser!.email ?? '';
      phoneController.text = widget.dataUser!.phone ?? '';
    }
  }

  Future<void> updateUser(String uid, String name, String email, String phone) async {
    try {
      DatabaseReference ref = FirebaseDatabase.instance.ref().child(USERS).child(uid);
      await ref.update({
        'name': name,
        'email': email,
        'phone': phone,
        'updatedAt': DateTime.now().millisecondsSinceEpoch,
      });
    } catch (error) {
      throw Exception('Failed to update user: $error');
    }
  }

  Future<void> deleteUser(String uid) async {
    try {
      DatabaseReference ref = FirebaseDatabase.instance.ref().child(USERS).child(uid);
      await ref.remove();
    } catch (error) {
      throw Exception('Failed to delete user: $error');
    }
  }

  Future<void> handleSubmit() async {
    if (!validate()) return;

    setState(() {
      isLoading = true;
    });

    try {
      if (widget.isEdit) {
        await updateUser(
          widget.dataUser!.uid!,
          nameController.text.trim(),
          emailController.text.trim(),
          phoneController.text.trim(),
        );
        if (mounted) {
          CustomSnackBar.showToast(context, 'User data updated successfully');
          Navigator.pop(context, true); // Return true to signal a refresh
        }
      } else {
        UserModel? userModel = await signUpWithEmailAndPassword(
          nameController.text.trim(),
          emailController.text.trim(),
          passwordController.text, // Use password from controller
          phoneController.text.trim(),
        );

        if (userModel != null && mounted) {
          CustomSnackBar.showToast(context, 'User created successfully');
          Navigator.pop(context, true); // Return true to signal a refresh
        }
      }
    } catch (e) {
      if (mounted) {
        CustomSnackBar.showToast(context, e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> handleDelete() async {
    if (widget.dataUser == null) return;

    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hapus User'),
        content: Text('Anda yakin ingin menghapus ${widget.dataUser!.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Hapus', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirm != true) return;

    setState(() {
      isLoading = true;
    });

    try {
      await deleteUser(widget.dataUser!.uid!);
      if (mounted) {
        CustomSnackBar.showToast(context, 'User deleted successfully');
        Navigator.pop(context, true); // Return true to signal a refresh
      }
    } catch (e) {
      if (mounted) {
        CustomSnackBar.showToast(context, e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.isEdit ? 'Update User' : 'Input User',
        ),
        actions: widget.isEdit
            ? [
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: isLoading ? null : handleDelete,
                  tooltip: 'Hapus User',
                )
              ]
            : null,
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        children: [
          const SizedBox(height: 30),
          Container(
            padding: const EdgeInsets.all(22),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: whiteColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomFormField(
                  title: 'Name',
                  controller: nameController,
                ),
                const SizedBox(height: 16),
                CustomFormField(
                  title: 'Email',
                  controller: emailController,
                  readOnly: widget.isEdit, // Make email field read-only in edit mode
                ),
                if (!widget.isEdit) ...[
                  const SizedBox(height: 16),
                  CustomFormField(
                    title: 'Password',
                    controller: passwordController,
                    obscureText: true,
                  ),
                ],
                const SizedBox(height: 16),
                CustomFormField(
                  title: 'Phone',
                  controller: phoneController,
                ),
                const SizedBox(height: 30),
                CustomFilledButton(
                  title: isLoading ? 'Loading...' : 'Continue',
                  onPressed: isLoading ? null : handleSubmit,
                ),
              ],
            ),
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}
