import 'package:espot/models/event_model.dart';
import 'package:espot/shared/cache_manager.dart';
import 'package:espot/ui/pages/data_events_input_page.dart';
import 'package:espot/ui/widgets/data_event_item.dart';
import 'package:espot/ui/widgets/buttons.dart';
import 'package:flutter/material.dart';
import 'package:espot/shared/theme.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:espot/shared/error_handler.dart';

class DataEventPage extends StatefulWidget with CacheManager {
  const DataEventPage({super.key});

  @override
  State<DataEventPage> createState() => _DataEventPageState();
}

class _DataEventPageState extends State<DataEventPage> {
  final searchController = TextEditingController(text: '');
  final DatabaseReference _dbRef =
      FirebaseDatabase.instance.ref().child('events');

  EventModel? selectedEvents;
  String searchResult = '';
  List<EventModel> eventsList = [];
  List<EventModel> filteredEventsList = [];
  bool isLoading = true;
  bool hasError = false;
  String errorMessage = '';
  String sortBy = 'name';

  @override
  void initState() {
    super.initState();
    // Load events data from Firebase
    fetchEvents();
  }

  // Fetch events data from Firebase
  Future<void> fetchEvents() async {
    if (mounted) {
      setState(() {
        isLoading = true;
        hasError = false;
        errorMessage = '';
      });
    }

    try {
      // Get snapshot from Firebase
      final DataSnapshot snapshot = await _dbRef.get();

      if (snapshot.exists) {
        eventsList.clear();
        final data = snapshot.value as Map<dynamic, dynamic>;
        data.forEach((key, value) {
          final EventModel event = EventModel.fromMap(value, key);
          eventsList.add(event);
        });
        // Initialize filtered list with all events
        filteredEventsList = List.from(eventsList);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          hasError = true;
          errorMessage = e.toString();
        });

        // Show user-friendly error dialog
        ErrorHandler.showFirebaseError(
          context,
          operation: 'loading events',
          error: e.toString(),
          onRetry: () {
            Navigator.of(context).pop();
            fetchEvents();
          },
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  // Delete event
  void deleteEvent() async {
    if (selectedEvents == null || selectedEvents!.uid == null) {
      return;
    }

    try {
      // Delete from Firebase
      await _dbRef.child(selectedEvents!.uid!).remove();

      // Remove from list and reset selection
      setState(() {
        eventsList.removeWhere((event) => event.uid == selectedEvents!.uid);
        filteredEventsList
            .removeWhere((event) => event.uid == selectedEvents!.uid);
        selectedEvents = null;
      });

      // Show success message
      if (mounted) {
        ErrorHandler.showSuccess(context,
            message: 'Event deleted successfully');
        Navigator.pushNamed(context, '/data-success-delete');
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showFirebaseError(
          context,
          operation: 'deleting event',
          error: e.toString(),
          onRetry: () {
            Navigator.of(context).pop();
            deleteEvent();
          },
        );
      }
    }
  }

  // Filter events based on search query
  void filterEvents(String query) {
    setState(() {
      searchResult = query;
      if (query.isEmpty) {
        filteredEventsList = List.from(eventsList);
      } else {
        filteredEventsList = eventsList.where((event) {
          return event.desc?.toLowerCase().contains(query.toLowerCase()) ??
              false;
        }).toList();
      }
      // Apply current sorting
      sortEvents(sortBy);
    });
  }

  // Clear search
  void clearSearch() {
    searchController.clear();
    filterEvents('');
  }

  // Sort events
  void sortEvents(String sortType) {
    setState(() {
      sortBy = sortType;
      switch (sortType) {
        case 'name':
          filteredEventsList.sort((a, b) => (a.desc ?? '')
              .toLowerCase()
              .compareTo((b.desc ?? '').toLowerCase()));
          break;
        case 'name_desc':
          filteredEventsList.sort((a, b) => (b.desc ?? '')
              .toLowerCase()
              .compareTo((a.desc ?? '').toLowerCase()));
          break;
      }
    });
  }

  // Show sort options
  void showSortOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Sort Events',
                style: blackTextStyle.copyWith(
                  fontSize: 18,
                  fontWeight: semiBold,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: Icon(
                  Icons.sort_by_alpha,
                  color: sortBy == 'name' ? redColor : greyColor,
                ),
                title: Text(
                  'Name (A-Z)',
                  style: blackTextStyle.copyWith(
                    fontWeight: sortBy == 'name' ? semiBold : medium,
                  ),
                ),
                trailing: sortBy == 'name'
                    ? Icon(Icons.check, color: redColor)
                    : null,
                onTap: () {
                  sortEvents('name');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.sort_by_alpha,
                  color: sortBy == 'name_desc' ? redColor : greyColor,
                ),
                title: Text(
                  'Name (Z-A)',
                  style: blackTextStyle.copyWith(
                    fontWeight: sortBy == 'name_desc' ? semiBold : medium,
                  ),
                ),
                trailing: sortBy == 'name_desc'
                    ? Icon(Icons.check, color: redColor)
                    : null,
                onTap: () {
                  sortEvents('name_desc');
                  Navigator.pop(context);
                },
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Events',
        ),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.pushNamed(context, '/event-input');
            },
            icon: const Icon(Icons.add),
            iconSize: 30,
          ),
        ],
        leading: IconButton(
          onPressed: () {
            Navigator.pushNamedAndRemoveUntil(
                context, '/home', (route) => false);
          },
          icon: const Icon(Icons.arrow_back),
          iconSize: 30,
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : hasError
              ? ErrorHandler.buildErrorWidget(
                  message: ErrorHandler.getFirebaseErrorMessage(errorMessage),
                  onRetry: fetchEvents,
                )
              : RefreshIndicator(
                  onRefresh: () async {
                    await fetchEvents();
                  },
                  child: ListView(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                    ),
                    children: [
                      const SizedBox(
                        height: 20,
                      ),
                      // Search Bar
                      Container(
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: lightGreyColor,
                          borderRadius: BorderRadius.circular(14),
                        ),
                        child: TextField(
                          controller: searchController,
                          onChanged: filterEvents,
                          decoration: InputDecoration(
                            hintText: 'Search events...',
                            hintStyle: greyTextStyle.copyWith(fontSize: 14),
                            prefixIcon: Icon(
                              Icons.search,
                              color: greyColor,
                            ),
                            suffixIcon: searchController.text.isNotEmpty
                                ? IconButton(
                                    icon: Icon(Icons.clear, color: greyColor),
                                    onPressed: clearSearch,
                                  )
                                : null,
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            'List Events',
                            style: blackTextStyle.copyWith(
                              fontSize: 16,
                              fontWeight: semiBold,
                            ),
                          ),
                          const Spacer(),
                          if (selectedEvents != null)
                            Text(
                              'Selected: ${selectedEvents!.desc}',
                              style: greyTextStyle.copyWith(
                                fontSize: 12,
                                fontStyle: FontStyle.italic,
                              ),
                            )
                          else
                            IconButton(
                              onPressed: showSortOptions,
                              icon: Icon(
                                Icons.sort,
                                color: greyColor,
                                size: 20,
                              ),
                              tooltip: 'Sort events',
                            ),
                        ],
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      // Show search results info
                      if (searchResult.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 10),
                          child: Text(
                            'Found ${filteredEventsList.length} result(s) for "$searchResult"',
                            style: greyTextStyle.copyWith(
                              fontSize: 12,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      filteredEventsList.isEmpty
                          ? Center(
                              child: Padding(
                                padding: const EdgeInsets.only(top: 50.0),
                                child: Column(
                                  children: [
                                    Icon(
                                      searchResult.isNotEmpty
                                          ? Icons.search_off
                                          : Icons.event_note,
                                      size: 64,
                                      color: greyColor,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      searchResult.isNotEmpty
                                          ? 'No events found for "$searchResult"'
                                          : 'No events found',
                                      style: blackTextStyle.copyWith(
                                        fontSize: 16,
                                        fontWeight: medium,
                                      ),
                                    ),
                                    if (searchResult.isNotEmpty) ...[
                                      const SizedBox(height: 8),
                                      TextButton(
                                        onPressed: clearSearch,
                                        child: Text(
                                          'Clear search',
                                          style: blueTextStyle.copyWith(
                                            fontSize: 14,
                                            fontWeight: medium,
                                          ),
                                        ),
                                      ),
                                    ] else ...[
                                      const SizedBox(height: 16),
                                      Text(
                                        'Create your first event to get started!',
                                        style: greyTextStyle.copyWith(
                                          fontSize: 14,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 16),
                                      ElevatedButton.icon(
                                        onPressed: () {
                                          Navigator.pushNamed(
                                              context, '/event-input');
                                        },
                                        icon: const Icon(Icons.add),
                                        label: const Text('Add Event'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: redColor,
                                          foregroundColor: whiteColor,
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 24,
                                            vertical: 12,
                                          ),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            )
                          : ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: filteredEventsList.length,
                              itemBuilder: (context, index) {
                                EventModel dataEvents =
                                    filteredEventsList[index];
                                return GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      // Toggle selection - if same item clicked, deselect
                                      if (selectedEvents?.uid ==
                                          dataEvents.uid) {
                                        selectedEvents = null;
                                      } else {
                                        selectedEvents = dataEvents;
                                      }
                                    });
                                  },
                                  child: DataEventItem(
                                    dataEvent: dataEvents,
                                    isSelected: selectedEvents != null
                                        ? selectedEvents!.uid == dataEvents.uid
                                        : false,
                                  ),
                                );
                              },
                            ),
                      const SizedBox(
                        height: 10,
                      ),
                    ],
                  ),
                ),
      floatingActionButton: selectedEvents != null
          ? Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CustomEditButton(
                  heroTag: "edit_event",
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DataEventInputPage(
                          data: selectedEvents!,
                        ),
                      ),
                    ).then((_) => fetchEvents());
                  },
                ),
                const SizedBox(width: 16),
                CustomDeleteButton(
                  heroTag: "delete_event",
                  onPressed: () {
                    // Show confirmation dialog
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Delete Event'),
                          content: Text(
                              'Are you sure you want to delete "${selectedEvents!.desc}"?'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('Cancel'),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                                EasyLoading.show(status: 'loading...');
                                deleteEvent();
                                EasyLoading.dismiss();
                              },
                              child: const Text('Delete',
                                  style: TextStyle(color: Colors.red)),
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
              ],
            )
          : null,
    );
  }
}
