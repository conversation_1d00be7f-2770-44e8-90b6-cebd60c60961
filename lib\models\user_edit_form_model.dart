

class UserEditFormModel {
  final String? name; // <PERSON>a pengguna yang diperbarui
  final String? phoneNumber; // Nomor telepon yang diperbarui
  final String? password; // Kata sandi yang diperbarui

  UserEditFormModel({
    this.name,
    this.phoneNumber,
    this.password,
  });

  Map<String, dynamic> to<PERSON>son() {
    return {
      'name': name,
      'phone_number': phoneNumber,
      'password': password,
    };
  }
}
