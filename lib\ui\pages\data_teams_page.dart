import 'package:espot/models/teams_model.dart';
import 'package:espot/shared/cache_manager.dart';
import 'package:espot/ui/pages/data_teams_input_page.dart';
import 'package:espot/ui/widgets/buttons.dart';
import 'package:flutter/material.dart';
import 'package:espot/shared/theme.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:espot/shared/error_handler.dart';
import 'package:espot/ui/widgets/data_teams_item.dart';

class DataTeamsPage extends StatefulWidget with CacheManager {
  const DataTeamsPage({super.key});

  @override
  State<DataTeamsPage> createState() => _DataTeamsPageState();
}

class _DataTeamsPageState extends State<DataTeamsPage> {
  final searchController = TextEditingController(text: '');
  final DatabaseReference _dbRef =
      FirebaseDatabase.instance.ref().child('teams');

  TeamsModel? selectedTeams;
  String searchResult = '';
  List<TeamsModel> teamsList = [];
  List<TeamsModel> filteredTeamsList = [];
  bool isLoading = true;
  bool hasError = false;
  String errorMessage = '';
  String sortBy = 'name'; //
  String filterBy = 'all'; //

  @override
  void initState() {
    super.initState();
    // Load teams data from Firebase
    fetchTeams();
  }

  // Fetch teams data from Firebase
  Future<void> fetchTeams() async {
    if (mounted) {
      setState(() {
        isLoading = true;
        hasError = false;
        errorMessage = '';
      });
    }

    try {
      // Get snapshot from Firebase
      final DataSnapshot snapshot = await _dbRef.get();

      if (snapshot.exists) {
        // Clear current list
        teamsList.clear();

        // Convert snapshot to map
        final data = snapshot.value as Map<dynamic, dynamic>;

        // Loop through each team and add to list
        data.forEach((key, value) {
          final TeamsModel team = TeamsModel.fromMap(value, key);
          teamsList.add(team);
        });
        // Initialize filtered list with all teams
        filteredTeamsList = List.from(teamsList);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          hasError = true;
          errorMessage = e.toString();
        });

        // Show user-friendly error dialog
        ErrorHandler.showFirebaseError(
          context,
          operation: 'loading teams',
          error: e.toString(),
          onRetry: () {
            Navigator.of(context).pop();
            fetchTeams();
          },
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  // Delete team
  void deleteTeam() async {
    if (selectedTeams == null || selectedTeams!.uid == null) {
      return;
    }

    try {
      // Delete from Firebase
      await _dbRef.child(selectedTeams!.uid!).remove();

      // Remove from list and reset selection
      setState(() {
        teamsList.removeWhere((team) => team.uid == selectedTeams!.uid);
        filteredTeamsList.removeWhere((team) => team.uid == selectedTeams!.uid);
        selectedTeams = null;
      });

      // Show success message
      if (mounted) {
        ErrorHandler.showSuccess(context, message: 'Team deleted successfully');
        Navigator.pushNamed(context, '/data-teams-success-delete');
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showFirebaseError(
          context,
          operation: 'deleting team',
          error: e.toString(),
          onRetry: () {
            Navigator.of(context).pop();
            deleteTeam();
          },
        );
      }
    }
  }

  // Filter teams based on search query and verification status
  void filterTeams(String query) {
    setState(() {
      searchResult = query;

      // First apply search filter
      List<TeamsModel> searchFiltered;
      if (query.isEmpty) {
        searchFiltered = List.from(teamsList);
      } else {
        searchFiltered = teamsList.where((team) {
          return team.desc?.toLowerCase().contains(query.toLowerCase()) ??
              false;
        }).toList();
      }

      // Then apply verification filter
      switch (filterBy) {
        case 'verified':
          filteredTeamsList =
              searchFiltered.where((team) => team.verified == 1).toList();
          break;
        case 'unverified':
          filteredTeamsList = searchFiltered
              .where((team) => team.verified == 0 || team.verified == null)
              .toList();
          break;
        case 'rejected':
          filteredTeamsList =
              searchFiltered.where((team) => team.verified == 2).toList();
          break;
        default: // 'all'
          filteredTeamsList = searchFiltered;
          break;
      }

      // Apply current sorting
      sortTeams(sortBy);
    });
  }

  // Clear search
  void clearSearch() {
    searchController.clear();
    filterTeams('');
  }

  // Filter by verification status
  void filterByVerification(String filter) {
    setState(() {
      filterBy = filter;
    });
    filterTeams(searchResult);
  }

  // Get display name for filter
  String _getFilterDisplayName(String filter) {
    switch (filter) {
      case 'verified':
        return 'Verified Teams';
      case 'unverified':
        return 'Unverified Teams';
      case 'rejected':
        return 'Rejected Teams';
      default:
        return 'All Teams';
    }
  }

  // Sort teams
  void sortTeams(String sortType) {
    setState(() {
      sortBy = sortType;
      switch (sortType) {
        case 'name':
          filteredTeamsList.sort((a, b) => (a.desc ?? '')
              .toLowerCase()
              .compareTo((b.desc ?? '').toLowerCase()));
          break;
        case 'name_desc':
          filteredTeamsList.sort((a, b) => (b.desc ?? '')
              .toLowerCase()
              .compareTo((a.desc ?? '').toLowerCase()));
          break;
      }
    });
  }

  // Show sort options
  void showSortOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Sort Teams',
                style: blackTextStyle.copyWith(
                  fontSize: 18,
                  fontWeight: semiBold,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: Icon(
                  Icons.sort_by_alpha,
                  color: sortBy == 'name' ? redColor : greyColor,
                ),
                title: Text(
                  'Name (A-Z)',
                  style: blackTextStyle.copyWith(
                    fontWeight: sortBy == 'name' ? semiBold : medium,
                  ),
                ),
                trailing: sortBy == 'name'
                    ? Icon(Icons.check, color: redColor)
                    : null,
                onTap: () {
                  sortTeams('name');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.sort_by_alpha,
                  color: sortBy == 'name_desc' ? redColor : greyColor,
                ),
                title: Text(
                  'Name (Z-A)',
                  style: blackTextStyle.copyWith(
                    fontWeight: sortBy == 'name_desc' ? semiBold : medium,
                  ),
                ),
                trailing: sortBy == 'name_desc'
                    ? Icon(Icons.check, color: redColor)
                    : null,
                onTap: () {
                  sortTeams('name_desc');
                  Navigator.pop(context);
                },
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  // Show filter options
  void showFilterOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Filter Teams',
                style: blackTextStyle.copyWith(
                  fontSize: 18,
                  fontWeight: semiBold,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: Icon(
                  Icons.list,
                  color: filterBy == 'all' ? redColor : greyColor,
                ),
                title: Text(
                  'All Teams',
                  style: blackTextStyle.copyWith(
                    fontWeight: filterBy == 'all' ? semiBold : medium,
                  ),
                ),
                trailing: filterBy == 'all'
                    ? Icon(Icons.check, color: redColor)
                    : null,
                onTap: () {
                  filterByVerification('all');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.verified,
                  color: filterBy == 'verified' ? redColor : greyColor,
                ),
                title: Text(
                  'Verified Teams',
                  style: blackTextStyle.copyWith(
                    fontWeight: filterBy == 'verified' ? semiBold : medium,
                  ),
                ),
                trailing: filterBy == 'verified'
                    ? Icon(Icons.check, color: redColor)
                    : null,
                onTap: () {
                  filterByVerification('verified');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.pending,
                  color: filterBy == 'unverified' ? redColor : greyColor,
                ),
                title: Text(
                  'Unverified Teams',
                  style: blackTextStyle.copyWith(
                    fontWeight: filterBy == 'unverified' ? semiBold : medium,
                  ),
                ),
                trailing: filterBy == 'unverified'
                    ? Icon(Icons.check, color: redColor)
                    : null,
                onTap: () {
                  filterByVerification('unverified');
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: Icon(
                  Icons.cancel,
                  color: filterBy == 'rejected' ? redColor : greyColor,
                ),
                title: Text(
                  'Rejected Teams',
                  style: blackTextStyle.copyWith(
                    fontWeight: filterBy == 'rejected' ? semiBold : medium,
                  ),
                ),
                trailing: filterBy == 'rejected'
                    ? Icon(Icons.check, color: redColor)
                    : null,
                onTap: () {
                  filterByVerification('rejected');
                  Navigator.pop(context);
                },
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  // Verify team
  void verifyTeam(int verificationStatus) async {
    if (selectedTeams == null || selectedTeams!.uid == null) {
      return;
    }

    try {
      // Update verification status in Firebase
      await _dbRef.child(selectedTeams!.uid!).update({
        'verified': verificationStatus,
      });

      // Update local data
      setState(() {
        // Update in main list
        final index =
            teamsList.indexWhere((team) => team.uid == selectedTeams!.uid);
        if (index != -1) {
          teamsList[index].verified = verificationStatus;
        }

        // Update in filtered list
        final filteredIndex = filteredTeamsList
            .indexWhere((team) => team.uid == selectedTeams!.uid);
        if (filteredIndex != -1) {
          filteredTeamsList[filteredIndex].verified = verificationStatus;
        }

        selectedTeams = null; // Deselect after verification
      });

      // Show success message
      String statusText = verificationStatus == 1
          ? 'verified'
          : verificationStatus == 2
              ? 'rejected'
              : 'unverified';
      if (mounted) {
        ErrorHandler.showSuccess(context,
            message: 'Team $statusText successfully');
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showFirebaseError(
          context,
          operation: 'updating team verification',
          error: e.toString(),
          onRetry: () {
            Navigator.of(context).pop();
            verifyTeam(verificationStatus);
          },
        );
      }
    }
  }

  // Show verification options
  void showVerificationOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Verify Team: ${selectedTeams?.desc}',
                style: blackTextStyle.copyWith(
                  fontSize: 18,
                  fontWeight: semiBold,
                ),
              ),
              const SizedBox(height: 20),
              ListTile(
                leading: const Icon(
                  Icons.verified,
                  color: Colors.green,
                ),
                title: Text(
                  'Approve Team',
                  style: blackTextStyle.copyWith(
                    fontWeight: medium,
                  ),
                ),
                subtitle: Text(
                  'Mark this team as verified',
                  style: greyTextStyle.copyWith(fontSize: 12),
                ),
                onTap: () {
                  Navigator.pop(context);
                  verifyTeam(1); // Verified
                },
              ),
              ListTile(
                leading: const Icon(
                  Icons.cancel,
                  color: Colors.red,
                ),
                title: Text(
                  'Reject Team',
                  style: blackTextStyle.copyWith(
                    fontWeight: medium,
                  ),
                ),
                subtitle: Text(
                  'Mark this team as rejected',
                  style: greyTextStyle.copyWith(fontSize: 12),
                ),
                onTap: () {
                  Navigator.pop(context);
                  verifyTeam(2); // Rejected
                },
              ),
              ListTile(
                leading: const Icon(
                  Icons.pending,
                  color: Colors.orange,
                ),
                title: Text(
                  'Reset to Unverified',
                  style: blackTextStyle.copyWith(
                    fontWeight: medium,
                  ),
                ),
                subtitle: Text(
                  'Remove verification status',
                  style: greyTextStyle.copyWith(fontSize: 12),
                ),
                onTap: () {
                  Navigator.pop(context);
                  verifyTeam(0); // Unverified
                },
              ),
              const SizedBox(height: 20),
            ],
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Teams',
        ),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.pushNamed(context, '/teams-input');
            },
            icon: const Icon(Icons.add),
            iconSize: 30,
          ),
        ],
        leading: IconButton(
          onPressed: () {
            Navigator.pushNamedAndRemoveUntil(
                context, '/home', (route) => false);
          },
          icon: const Icon(Icons.arrow_back),
          iconSize: 30,
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : hasError
              ? ErrorHandler.buildErrorWidget(
                  message: ErrorHandler.getFirebaseErrorMessage(errorMessage),
                  onRetry: fetchTeams,
                )
              : RefreshIndicator(
                  onRefresh: () async {
                    await fetchTeams();
                  },
                  child: ListView(
                    // physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                    ),
                    children: [
                      const SizedBox(
                        height: 20,
                      ),
                      // Search Bar
                      Container(
                        margin: const EdgeInsets.only(bottom: 20),
                        decoration: BoxDecoration(
                          color: lightGreyColor,
                          borderRadius: BorderRadius.circular(14),
                        ),
                        child: TextField(
                          controller: searchController,
                          onChanged: filterTeams,
                          decoration: InputDecoration(
                            hintText: 'Search teams...',
                            hintStyle: greyTextStyle.copyWith(fontSize: 14),
                            prefixIcon: Icon(
                              Icons.search,
                              color: greyColor,
                            ),
                            suffixIcon: searchController.text.isNotEmpty
                                ? IconButton(
                                    icon: Icon(Icons.clear, color: greyColor),
                                    onPressed: clearSearch,
                                  )
                                : null,
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 16,
                            ),
                          ),
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            'List Teams',
                            style: blackTextStyle.copyWith(
                              fontSize: 16,
                              fontWeight: semiBold,
                            ),
                          ),
                          const Spacer(),
                          if (selectedTeams != null)
                            Text(
                              'Selected: ${selectedTeams!.desc}',
                              style: greyTextStyle.copyWith(
                                fontSize: 12,
                                fontStyle: FontStyle.italic,
                              ),
                            )
                          else
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  onPressed: showFilterOptions,
                                  icon: Icon(
                                    Icons.filter_list,
                                    color: filterBy != 'all'
                                        ? redColor
                                        : greyColor,
                                    size: 20,
                                  ),
                                  tooltip: 'Filter teams',
                                ),
                                IconButton(
                                  onPressed: showSortOptions,
                                  icon: Icon(
                                    Icons.sort,
                                    color: greyColor,
                                    size: 20,
                                  ),
                                  tooltip: 'Sort teams',
                                ),
                              ],
                            ),
                        ],
                      ),
                      const SizedBox(
                        height: 15,
                      ),
                      // Show search and filter results info
                      if (searchResult.isNotEmpty || filterBy != 'all')
                        Padding(
                          padding: const EdgeInsets.only(bottom: 10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (searchResult.isNotEmpty)
                                Text(
                                  'Search: "$searchResult"',
                                  style: greyTextStyle.copyWith(
                                    fontSize: 12,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              if (filterBy != 'all')
                                Text(
                                  'Filter: ${_getFilterDisplayName(filterBy)}',
                                  style: greyTextStyle.copyWith(
                                    fontSize: 12,
                                    fontStyle: FontStyle.italic,
                                  ),
                                ),
                              Text(
                                'Found ${filteredTeamsList.length} result(s)',
                                style: greyTextStyle.copyWith(
                                  fontSize: 12,
                                  fontWeight: medium,
                                ),
                              ),
                            ],
                          ),
                        ),
                      filteredTeamsList.isEmpty
                          ? Center(
                              child: Padding(
                                padding: const EdgeInsets.only(top: 50.0),
                                child: Column(
                                  children: [
                                    Icon(
                                      searchResult.isNotEmpty
                                          ? Icons.search_off
                                          : Icons.groups,
                                      size: 64,
                                      color: greyColor,
                                    ),
                                    const SizedBox(height: 16),
                                    Text(
                                      searchResult.isNotEmpty
                                          ? 'No teams found for "$searchResult"'
                                          : 'No teams found',
                                      style: blackTextStyle.copyWith(
                                        fontSize: 16,
                                        fontWeight: medium,
                                      ),
                                    ),
                                    if (searchResult.isNotEmpty ||
                                        filterBy != 'all') ...[
                                      const SizedBox(height: 8),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          if (searchResult.isNotEmpty)
                                            TextButton(
                                              onPressed: clearSearch,
                                              child: Text(
                                                'Clear search',
                                                style: blueTextStyle.copyWith(
                                                  fontSize: 14,
                                                  fontWeight: medium,
                                                ),
                                              ),
                                            ),
                                          if (searchResult.isNotEmpty &&
                                              filterBy != 'all')
                                            Text(' | ', style: greyTextStyle),
                                          if (filterBy != 'all')
                                            TextButton(
                                              onPressed: () =>
                                                  filterByVerification('all'),
                                              child: Text(
                                                'Clear filter',
                                                style: blueTextStyle.copyWith(
                                                  fontSize: 14,
                                                  fontWeight: medium,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ] else ...[
                                      const SizedBox(height: 16),
                                      Text(
                                        'Create your first team to get started!',
                                        style: greyTextStyle.copyWith(
                                          fontSize: 14,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 16),
                                      ElevatedButton.icon(
                                        onPressed: () {
                                          Navigator.pushNamed(
                                              context, '/teams-input');
                                        },
                                        icon: const Icon(Icons.add),
                                        label: const Text('Add Team'),
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: redColor,
                                          foregroundColor: whiteColor,
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 24,
                                            vertical: 12,
                                          ),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            )
                          : ListView.builder(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: filteredTeamsList.length,
                              itemBuilder: (context, index) {
                                TeamsModel dataTeams = filteredTeamsList[index];
                                return GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        // Toggle selection - if same item clicked, deselect
                                        if (selectedTeams?.uid ==
                                            dataTeams.uid) {
                                          selectedTeams = null;
                                        } else {
                                          selectedTeams = dataTeams;
                                        }
                                      });
                                    },
                                    child: DataTeamsItem(
                                      dataTeams: dataTeams,
                                      isSelected: selectedTeams != null
                                          ? selectedTeams!.uid == dataTeams.uid
                                          : false,
                                    ));
                              },
                            ),
                      const SizedBox(
                        height: 10,
                      ),
                    ],
                  ),
                ),
      floatingActionButton: selectedTeams != null
          ? Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CustomEditButton(
                  heroTag: "edit_team",
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => DataTeamsInputPage(
                          data: selectedTeams!,
                        ),
                      ),
                    ).then((_) => fetchTeams());
                  },
                ),
                const SizedBox(width: 16),
                CustomDeleteButton(
                  heroTag: "delete_team",
                  onPressed: () {
                    // Show confirmation dialog
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Delete Team'),
                          content: Text(
                              'Are you sure you want to delete "${selectedTeams!.desc}"?'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              child: const Text('Cancel'),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                                EasyLoading.show(status: 'loading...');
                                deleteTeam();
                                EasyLoading.dismiss();
                              },
                              child: const Text('Delete',
                                  style: TextStyle(color: Colors.red)),
                            ),
                          ],
                        );
                      },
                    );
                  },
                ),
                const SizedBox(width: 16),
                CustomVerifyButton(
                  heroTag: "verify_team",
                  onPressed: () {
                    showVerificationOptions();
                  },
                ),
              ],
            )
          : null,
    );
  }
}
