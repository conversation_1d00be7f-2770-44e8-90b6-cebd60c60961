import 'package:flutter/material.dart';
import 'package:espot/shared/theme.dart';
import 'package:espot/ui/widgets/buttons.dart';

class DataSuccessDeletePage extends StatelessWidget {
  final String? sourceType; // 'event', 'teams', atau null untuk default

  const DataSuccessDeletePage({
    this.sourceType,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Nice Delete!',
              style: blackTextStyle.copyWith(
                fontSize: 20,
                fontWeight: semiBold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 26,
            ),
            Text(
              'Data has been\ndeleted',
              style: greyTextStyle.copyWith(
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 50,
            ),
            CustomFilledButton(
              width: 183,
              title: 'Back',
              onPressed: () {
                // Kembali ke halaman sesuai sumber
                if (sourceType == 'event') {
                  Navigator.pushNamedAndRemoveUntil(
                      context, '/event', (route) => false);
                } else if (sourceType == 'teams') {
                  Navigator.pushNamedAndRemoveUntil(
                      context, '/teams', (route) => false);
                } else {
                  // Default ke home jika sourceType tidak dikenali
                  Navigator.pushNamedAndRemoveUntil(
                      context, '/home', (route) => false);
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
