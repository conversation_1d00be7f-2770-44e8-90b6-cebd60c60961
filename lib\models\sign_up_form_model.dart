

class SignUpFormModel {
  final String? name; // <PERSON>a pengguna
  final String? phoneNumber; // Nomor telepon pengguna
  final String? password; // Kata sandi pengguna
  final String? profilePicture; // URL/path foto profil
  SignUpFormModel({
    this.name,
    this.phoneNumber,
    this.password,
    this.profilePicture,
  });

  Map<String, dynamic> to<PERSON>son() {
    return {
      'name': name,
      'phone_number': phoneNumber,
      'password': password,
      'profile_picture': profilePicture,
    };
  }

  SignUpFormModel copyWith({
    String? name,
    String? phoneNumber,
    String? password,
    String? profilePicture,
  }) =>
      SignUpFormModel(
        name: name ?? this.name,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        password: password ?? this.password,
        profilePicture: profilePicture ?? this.profilePicture,
      );
}
