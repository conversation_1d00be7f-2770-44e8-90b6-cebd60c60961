import 'package:espot/models/user_model.dart';
import 'package:get_storage/get_storage.dart';


mixin CacheManager {
  Future<bool> saveUser(UserModel? response) async {
    final box = GetStorage();
    await box.write(CacheManagerKey.ID.toString(), response!.uid);
    await box.write(CacheManagerKey.NAME.toString(), response.name);
    await box.write(CacheManagerKey.PHONE.toString(), response.phone);
    await box.write(CacheManagerKey.EMAIL.toString(), response.email);
    await box.write(CacheManagerKey.PHOTO.toString(), response.profilePicture);
    return true;
  }

  Future<bool> savePhoto(String? url) async {
    final box = GetStorage();
    await box.write(CacheManagerKey.PHOTO.toString(), url);
    return true;
  }

  String? getId() {
    final box = GetStorage();
    return box.read(CacheManagerKey.ID.toString()) ?? '';
  }

  String? getName() {
    final box = GetStorage();
    return box.read(CacheManagerKey.NAME.toString()) ?? 'Admin';
  }

  String? getPHONE() {
    final box = GetStorage();
    return box.read(CacheManagerKey.PHONE.toString()) ?? '-';
  }

  String? getEmail() {
    final box = GetStorage();
    return box.read(CacheManagerKey.EMAIL.toString()) ?? '<EMAIL>';
  }

  String? getPhoto() {
    final box = GetStorage();
    return box.read(CacheManagerKey.PHOTO.toString()) ?? '';
  }

  int? getVerified() {
    final box = GetStorage();
    return box.read(CacheManagerKey.VERIFIED.toString()) ?? 0;
  }

  Future<bool> saveVerified(int? verified) async {
    final box = GetStorage();
    await box.write(CacheManagerKey.VERIFIED.toString(), verified);
    return true;
  }

  Future<void> removeAll() async {
    final box = GetStorage();
    await box.remove(CacheManagerKey.ID.toString());
    await box.remove(CacheManagerKey.NAME.toString());
    await box.remove(CacheManagerKey.PHONE.toString());
    await box.remove(CacheManagerKey.EMAIL.toString());
    await box.remove(CacheManagerKey.PHOTO.toString());
    await box.remove(CacheManagerKey.VERIFIED.toString());
  }
}

// ignore: constant_identifier_names
enum CacheManagerKey { ID, NAME, PHONE, EMAIL, PHOTO, VERIFIED }
