import 'package:flutter/material.dart';
import 'package:espot/shared/theme.dart';
import 'package:espot/shared/snackbar.dart';

class ErrorHandler {
  // Show error dialog with retry option
  static void showErrorDialog(
    BuildContext context, {
    required String title,
    required String message,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(
                Icons.error_outline,
                color: Colors.red,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: blackTextStyle.copyWith(
                  fontSize: 18,
                  fontWeight: semiBold,
                ),
              ),
            ],
          ),
          content: Text(
            message,
            style: blackTextStyle.copyWith(
              fontSize: 14,
            ),
          ),
          actions: [
            if (onCancel != null)
              TextButton(
                onPressed: onCancel,
                child: Text(
                  'Cancel',
                  style: greyTextStyle.copyWith(
                    fontSize: 14,
                    fontWeight: medium,
                  ),
                ),
              ),
            if (onRetry != null)
              ElevatedButton(
                onPressed: onRetry,
                style: ElevatedButton.styleFrom(
                  backgroundColor: redColor,
                  foregroundColor: whiteColor,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
                child: Text(
                  'Retry',
                  style: whiteTextStyle.copyWith(
                    fontSize: 14,
                    fontWeight: medium,
                  ),
                ),
              ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'OK',
                style: blueTextStyle.copyWith(
                  fontSize: 14,
                  fontWeight: medium,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Show network error with specific message
  static void showNetworkError(
    BuildContext context, {
    VoidCallback? onRetry,
  }) {
    showErrorDialog(
      context,
      title: 'Connection Error',
      message: 'Please check your internet connection and try again.',
      onRetry: onRetry,
    );
  }

  // Show Firebase error with user-friendly message
  static void showFirebaseError(
    BuildContext context, {
    required String operation,
    required String error,
    VoidCallback? onRetry,
  }) {
    String userFriendlyMessage = getFirebaseErrorMessage(error);

    showErrorDialog(
      context,
      title: 'Error $operation',
      message: userFriendlyMessage,
      onRetry: onRetry,
    );
  }

  // Convert Firebase error to user-friendly message
  static String getFirebaseErrorMessage(String error) {
    if (error.contains('network')) {
      return 'Network connection failed. Please check your internet connection.';
    } else if (error.contains('permission')) {
      return 'Access denied. Please check your permissions.';
    } else if (error.contains('timeout')) {
      return 'Request timed out. Please try again.';
    } else if (error.contains('unavailable')) {
      return 'Service temporarily unavailable. Please try again later.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  // Show success message
  static void showSuccess(
    BuildContext context, {
    required String message,
  }) {
    CustomSnackBar.showToast(context, message);
  }

  // Show loading error with retry
  static Widget buildErrorWidget({
    required String message,
    VoidCallback? onRetry,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.withValues(alpha: 0.6),
            ),
            const SizedBox(height: 16),
            Text(
              'Oops! Something went wrong',
              style: blackTextStyle.copyWith(
                fontSize: 18,
                fontWeight: semiBold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: greyTextStyle.copyWith(
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: redColor,
                  foregroundColor: whiteColor,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
