{"buildFiles": ["C:\\dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["c:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\kampus\\Semester 4\\pemrograman mobile\\espot\\android\\app\\.cxx\\Debug\\6l6p634g\\x86_64", "clean"]], "buildTargetsCommandComponents": ["c:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\kampus\\Semester 4\\pemrograman mobile\\espot\\android\\app\\.cxx\\Debug\\6l6p634g\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}