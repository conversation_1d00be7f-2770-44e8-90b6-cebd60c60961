import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../../models/teams_model.dart';
import '../../ui/pages/data_teams_input_page.dart';
import 'buttons.dart';

class TeamsActionButtons extends StatelessWidget {
  final TeamsModel selectedTeam;
  final VoidCallback onEdit;
  final VoidCallback onDelete;
  final VoidCallback onVerify;

  const TeamsActionButtons({
    super.key,
    required this.selectedTeam,
    required this.onEdit,
    required this.onDelete,
    required this.onVerify,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        CustomEditButton(
          heroTag: "edit_team",
          onPressed: onEdit,
        ),
        const SizedBox(width: 16),
        CustomDeleteButton(
          heroTag: "delete_team",
          onPressed: () => _showDeleteConfirmation(context),
        ),
        const SizedBox(width: 16),
        CustomVerifyButton(
          heroTag: "verify_team",
          onPressed: onVerify,
        ),
      ],
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Team'),
          content: Text('Are you sure you want to delete "${selectedTeam.desc}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                EasyLoading.show(status: 'loading...');
                onDelete();
                EasyLoading.dismiss();
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
