import 'package:espot/ui/pages/data_event_page.dart';
import 'package:espot/ui/pages/data_events_input_page.dart';
import 'package:espot/ui/pages/data_success_delete_page.dart';
import 'package:espot/ui/pages/data_success_page.dart';
import 'package:espot/ui/pages/data_success_update_page.dart';
import 'package:espot/ui/pages/data_teams_input_page.dart';
import 'package:espot/ui/pages/data_teams_page.dart';
import 'package:espot/ui/pages/data_users_input_page.dart';
import 'package:espot/ui/pages/data_users_page.dart';
import 'package:espot/ui/pages/home_page.dart';
import 'package:espot/ui/pages/onboarding_page.dart';
import 'package:espot/ui/pages/sign_in_page.dart';
import 'package:espot/ui/pages/sign_up_page.dart';
import 'package:espot/ui/pages/sign_up_success_page.dart';
import 'package:espot/ui/pages/splash_page.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:espot/shared/theme.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        appBarTheme: AppBarTheme(
            backgroundColor: lightBackgroundColor,
            elevation: 0,
            centerTitle: true,
            iconTheme: IconThemeData(
              color: blackColor,
            ),
            titleTextStyle:
                blackTextStyle.copyWith(fontSize: 20, fontWeight: semiBold)),
      ),
      routes: {
        '/': (context) => const SplashPage(),
        '/onboarding': (context) => const OnboardingPage(),
        '/sign-in': (context) => const SignInPage(),
        '/sign-up': (context) => const SignUpPage(),
        '/sign-up-success': (context) => const SignUpSuccessPage(),
        '/home': (context) => const HomePage(),
        '/users': (context) => const DataUsersPage(),
        '/user-input': (context) => const DataUsersInputPage(),
        '/user-succes': (context) => const DataSuccessPage(),
        '/user-succes-update': (context) => const DataSuccessUpdatePage(),
        '/user-succes-delate': (context) => const DataSuccessDeletePage(),
        '/event': (context) => const DataEventPage(),
        '/event-input': (context) => const DataEventInputPage(),
        '/teams': (context) => const DataTeamsPage(),
        '/teams-input': (context) => const DataTeamsInputPage(),
        '/data-success': (context) =>
            const DataSuccessPage(sourceType: 'event'),
        '/data-success-update': (context) =>
            const DataSuccessUpdatePage(sourceType: 'event'),
        '/data-success-delete': (context) =>
            const DataSuccessDeletePage(sourceType: 'event'),
        '/data-teams-success': (context) =>
            const DataSuccessPage(sourceType: 'teams'),
        '/data-teams-success-update': (context) =>
            const DataSuccessUpdatePage(sourceType: 'teams'),
        '/data-teams-success-delete': (context) =>
            const DataSuccessDeletePage(sourceType: 'teams'),
      },
      builder: EasyLoading.init(),
    );
  }
}
