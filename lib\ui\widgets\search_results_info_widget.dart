import 'package:flutter/material.dart';
import '../../shared/theme.dart';

class SearchResultsInfoWidget extends StatelessWidget {
  final String searchResult;
  final String filterBy;
  final int resultsCount;
  final String Function(String) getFilterDisplayName;

  const SearchResultsInfoWidget({
    super.key,
    required this.searchResult,
    required this.filterBy,
    required this.resultsCount,
    required this.getFilterDisplayName,
  });

  @override
  Widget build(BuildContext context) {
    if (searchResult.isEmpty && filterBy == 'all') {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (searchResult.isNotEmpty)
            Text(
              'Search: "$searchResult"',
              style: greyTextStyle.copyWith(
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          if (filterBy != 'all')
            Text(
              'Filter: ${getFilterDisplayName(filterBy)}',
              style: greyTextStyle.copyWith(
                fontSize: 12,
                fontStyle: FontStyle.italic,
              ),
            ),
          Text(
            'Found $resultsCount result(s)',
            style: greyTextStyle.copyWith(
              fontSize: 12,
              fontWeight: medium,
            ),
          ),
        ],
      ),
    );
  }
}
